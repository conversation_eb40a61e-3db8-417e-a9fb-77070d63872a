import { withInfoPlist } from "expo/config-plugins";

export default () => {
  return {
    name: process.env.APP_ENV === "production" ? "Sphere" : "Sphere Preview",
    slug: "sphere-app",
    version: "0.1.5",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "myapp",
    userInterfaceStyle: "dark",
    newArchEnabled: true,
    ios: {
      supportsTablet: false,
      // bundleIdentifier:
      //   process.env.APP_ENV === "production"
      //     ? "com.slostudio.sphere"
      //     : "com.slostudio.spherebeta",
      bundleIdentifier: "com.slostudio.spherebeta",
      // icon: {
      //   dark: "./assets/images/icon.png",
      //   light: "./assets/images/icon.png",
      //   tinted: "./assets/images/icon.png",
      // },
      icon: "./assets/images/app-icon.png",
      googleServicesFile: "./GoogleService-Info.plist",
      infoPlist: {
        UIBackgroundModes: ["remote-notification"],
      },
      entitlements: {
        "aps-environment": "production",
      },
      associatedDomains: ["applinks:sphere-app.expo.app"],
      // associatedDomains: ["applinks:behemoth.ngrok.io"],
    },
    android: {
      // adaptiveIcon: {
      //   foregroundImage: "./assets/images/adaptive-icon.png",
      //   backgroundColor: "#3B4BB4",
      // },
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        // backgroundColor: "#ffffff",
      },
      package:
        process.env.APP_ENV === "production"
          ? "com.slostudio.sphere"
          : "com.slostudio.spherebeta",
      googleServicesFile: "./google-services.json",
      intentFilters: [
        {
          action: "VIEW",
          autoVerify: true,
          data: [
            {
              scheme: "https",
              host: "sphere-app.expo.app",
              pathPattern: "/groups/.*/cohorts/.*/modules/.*/live-classes/.*",
            },
            {
              scheme: "https",
              host: "sphere-app.expo.app",
              pathPattern: "/groups/.*",
            },
          ],
          category: ["BROWSABLE", "DEFAULT"],
        },
      ],
    },
    web: {
      bundler: "metro",
      output: "server",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/splash-screen.png",
          imageWidth: 200,
          resizeMode: "contain",
          backgroundColor: "#000000",
          dark: {
            image: "./assets/images/splash-screen.png",
            imageWidth: 200,
            resizeMode: "contain",
            backgroundColor: "#000000",
          },
        },
      ],
      "expo-secure-store",
      [
        "expo-video",
        {
          supportsPictureInPicture: true,
        },
      ],
      [
        "expo-build-properties",
        {
          android: {
            minSdkVersion: 26,
          },
          ios: {
            useFrameworks: "static",
          },
        },
      ],
      "@react-native-firebase/app",
      "@react-native-firebase/messaging",
      [
        "@react-native-google-signin/google-signin",
        {
          iosUrlScheme:
            "com.googleusercontent.apps.268628417819-abg0agb9a7hh990f6iccku8e1442ibek",
        },
      ],
      [
        "expo-image-picker",
        {
          photosPermission:
            "The app accesses your photos to let you update your avatar.",
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {
        origin: false,
      },
      eas: {
        projectId: "a38518de-7c25-4057-bf86-61ef005915f9",
      },
    },
    owner: "slo-studio",
  };
};
