import React, { useState, useRef, useCallback } from "react";
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  Dimensions,
  TextInputProps,
} from "react-native";
import { Image } from "expo-image";

import { useUsersForMentions } from "@/lib/api/queries";
import {
  parseMentions,
  searchUsersForMention,
  getMentionInsertPosition,
  insertMention,
  type MentionUser,
  type ParsedMention,
} from "@/lib/utils/mentions";

interface MentionInputProps
  extends Omit<TextInputProps, "onChangeText" | "value"> {
  value: string;
  onChangeText: (text: string, mentions: ParsedMention[]) => void;
  placeholder?: string;
  maxLength?: number;
  multiline?: boolean;
  style?: any;
}

const { width: screenWidth } = Dimensions.get("window");

export const MentionInput: React.FC<MentionInputProps> = ({
  value,
  onChangeText,
  placeholder = "Write a comment...",
  maxLength = 500,
  multiline = true,
  style,
  ...textInputProps
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [mentionQuery, setMentionQuery] = useState("");
  const [mentionPosition, setMentionPosition] = useState<{
    start: number;
    end: number;
  } | null>(null);
  const [cursorPosition, setCursorPosition] = useState(0);

  const textInputRef = useRef<TextInput>(null);
  const { data: usersData } = useUsersForMentions();
  const availableUsers = usersData || [];

  // Search for users based on mention query
  const suggestedUsers = searchUsersForMention(mentionQuery, availableUsers, 5);

  const handleTextChange = useCallback(
    (text: string) => {
      // Parse mentions from the text
      const parseResult = parseMentions(text, availableUsers);

      // Call the parent's onChangeText with parsed mentions
      onChangeText(text, parseResult.mentions);
    },
    [availableUsers, onChangeText]
  );

  const handleSelectionChange = useCallback(
    (event: any) => {
      const { start } = event.nativeEvent.selection;
      setCursorPosition(start);

      // Check if cursor is in a mention position
      const mentionPos = getMentionInsertPosition(value, start);

      if (mentionPos) {
        setMentionPosition({ start: mentionPos.start, end: mentionPos.end });
        setMentionQuery(mentionPos.query);
        setShowSuggestions(true);
      } else {
        setShowSuggestions(false);
        setMentionPosition(null);
        setMentionQuery("");
      }
    },
    [value]
  );

  const handleMentionSelect = useCallback(
    (user: MentionUser) => {
      if (!mentionPosition) return;

      const result = insertMention(value, user, mentionPosition);

      // Update text and cursor position
      handleTextChange(result.text);

      // Hide suggestions
      setShowSuggestions(false);
      setMentionPosition(null);
      setMentionQuery("");

      // Focus back to input and set cursor position
      setTimeout(() => {
        textInputRef.current?.focus();
        textInputRef.current?.setNativeProps({
          selection: {
            start: result.cursorPosition,
            end: result.cursorPosition,
          },
        });
      }, 100);
    },
    [value, mentionPosition, handleTextChange]
  );

  const renderSuggestionItem = ({ item: user }: { item: MentionUser }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleMentionSelect(user)}
    >
      {user.avatarUrl ? (
        <Image
          source={{ uri: user.avatarUrl }}
          style={styles.suggestionAvatar}
        />
      ) : (
        <View style={styles.suggestionAvatarPlaceholder}>
          <Text style={styles.suggestionAvatarText}>
            {user.name[0]?.toUpperCase() || "U"}
          </Text>
        </View>
      )}
      <View style={styles.suggestionInfo}>
        <Text style={styles.suggestionName}>{user.name}</Text>
        {user.username && (
          <Text style={styles.suggestionUsername}>@{user.username}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TextInput
        ref={textInputRef}
        style={[styles.input, style]}
        value={value}
        onChangeText={handleTextChange}
        onSelectionChange={handleSelectionChange}
        placeholder={placeholder}
        placeholderTextColor="#9A9A9A"
        maxLength={maxLength}
        multiline={multiline}
        {...textInputProps}
      />

      {/* Mention Suggestions Modal */}
      <Modal
        visible={showSuggestions && suggestedUsers.length > 0}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSuggestions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSuggestions(false)}
        >
          <View style={styles.suggestionsContainer}>
            <FlatList
              data={suggestedUsers}
              renderItem={renderSuggestionItem}
              keyExtractor={(item) => item.id}
              style={styles.suggestionsList}
              keyboardShouldPersistTaps="handled"
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  input: {
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    padding: 12,
    color: "#fff",
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    justifyContent: "flex-end",
  },
  suggestionsContainer: {
    backgroundColor: "#171D23",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: 300,
    paddingVertical: 8,
  },
  suggestionsList: {
    maxHeight: 250,
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  suggestionAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  suggestionAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
  },
  suggestionAvatarText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  suggestionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  suggestionName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  suggestionUsername: {
    color: "#9A9A9A",
    fontSize: 14,
    marginTop: 2,
  },
});
